
## 🔥 Idhu lam edhukku pandrom?

### ✅ `javac -d folder One.java`

* **Purpose**: `.java` file-ah compile pannitu, `.class` file-ah **separate folder** la vechikkanum.
* 👨‍💻 JVM runs only `.class`, not `.java`.

### ✅ `java -cp folder One`

* **Purpose**: `docs` folder-la irukkura `.class` file-ah run panradhuku.

### ✅ `javadoc -d docfile One.java`

* **Purpose**: Java-ku **HTML documentation** create pannradhuku.
* Like help files → class, method, comments explain pannum.

---

## 💡 Summary (Tamil + English)

| Command                       | Edhukku? (Why)                               |
| ----------------------------- | -------------------------------------------- |
| `javac -d docs One.java`      | `.class` file separate folder la save panna  |
| `java -cp docs One`           | Adha run panna (JVM uses `.class`)           |
| `javadoc -d docfile One.java` | HTML docs create panna (project explain use) |

---

Need next topic? Example: `package`, `class`, `object`, or OOP?

Sonnaru bro, naan teaching ready ✌️🔥




types of java
1. Java SE (SOFTWARE EDITION) -> SYSTEM BASED APPLICATION
2. Java EE (ENTERPRISE EDITION) -> WEB BASED APPLICATION
3. Java ME (MOBILE/MICRO/MINIMALISED EDITION) -> MOBILE BASED APPLICATION
4. JavaFX -> GUI BASED APPLICATION - CROSS PLATFORM APPLICATION

PUBLIC           - JAVA HAS FOURTH ACCESS SPECIFIER - DEFAULT - IF NOTHING MENTIONED, IT IS A DEFAULT. IT HAS PACKAGE(FOLDER) LEVEL ACCESS
STATIC           - TO CREATE MEMORY INSIDE STACK , WITHOUT CREATING OBJECT, CREATE MEMORY
VOID             - COMPENSATION OF RETURN(KEYWORD FOR PROCESSOR) 0(SUCCESS NOTE TO DEALLOCATE BALANCE MEMORY) - 'VOID' IN CLASS 
STRING ARGS[]    - 

ACCESSING CAN BE DONE ONLY BY USING CLASS NAME
MAIN CAN HAVE ONLY 2 PARAMS - SIZE AND TYPE           - COMMAND LINE ARGUEMENTS (BEFORE STARTING PROCESS, GIVING INPUT)
TYPE CASTING - Integer.parseInt(args[0])
what is PSV
WHAT ARE COMMAND LINE ARGUEMENTS


"The Object class in the java.lang package serves as the ultimate super class for all classes in Java,
 providing fundamental methods like toString(), equals(), and hashCode() that every object inherits by default."