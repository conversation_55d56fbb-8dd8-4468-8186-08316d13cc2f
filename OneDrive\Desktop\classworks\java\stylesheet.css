/*
 * Javadoc style sheet
 */

@import url('resources/fonts/dejavu.css');

/*
 * These CSS custom properties (variables) define the core color and font
 * properties used in this stylesheet.
 */
:root {
    /* body, block and code fonts */
    --body-font-family: 'DejaVu Sans', Arial, Helvetica, sans-serif;
    --block-font-family: 'DejaVu Serif', Georgia, "Times New Roman", Times, serif;
    --code-font-family: 'DejaVu Sans Mono', monospace;
    /* Base font sizes for body and code elements */
    --body-font-size: 14px;
    --code-font-size: 14px;
    /* Text colors for body and block elements */
    --body-text-color: #353833;
    --block-text-color: #474747;
    /* Background colors for various structural elements */
    --body-background-color: #ffffff;
    --section-background-color: #f8f8f8;
    --detail-background-color: #ffffff;
    /* Colors for navigation bar and table captions */
    --navbar-background-color: #4D7A97;
    --navbar-text-color: #ffffff;
    /* Background color for subnavigation and various headers */
    --subnav-background-color: #dee3e9;
    /* Background and text colors for selected tabs and navigation items */
    --selected-background-color: #f8981d;
    --selected-text-color: #253441;
    --selected-link-color: #1f389c;
    /* Background colors for generated tables */
    --even-row-color: #ffffff;
    --odd-row-color: #eeeeef;
    /* Text color for page title */
    --title-color: #2c4557;
    /* Text colors for links */
    --link-color: #4A6782;
    --link-color-active: #bb7a2a;
    /* Snippet colors */
    --snippet-background-color: #ebecee;
    --snippet-text-color: var(--block-text-color);
    --snippet-highlight-color: #f7c590;
    /* Border colors for structural elements and user defined tables */
    --border-color: #ededed;
    --table-border-color: #000000;
    /* Search input colors */
    --search-input-background-color: #ffffff;
    --search-input-text-color: #000000;
    --search-input-placeholder-color: #909090;
    /* Highlight color for active search tag target */
    --search-tag-highlight-color: #ffff00;
    /* Adjustments for icon and active background colors of copy-to-clipboard buttons */
    --copy-icon-brightness: 100%;
    --copy-button-background-color-active: rgba(168, 168, 176, 0.3);
    /* Colors for invalid tag notifications */
    --invalid-tag-background-color: #ffe6e6;
    --invalid-tag-text-color: #000000;
}
/*
 * Styles for individual HTML elements.
 *
 * These are styles that are specific to individual HTML elements. Changing them affects the style of a particular
 * HTML element throughout the page.
 */
body {
    background-color:var(--body-background-color);
    color:var(--body-text-color);
    font-family:var(--body-font-family);
    font-size:var(--body-font-size);
    margin:0;
    padding:0;
    height:100%;
    width:100%;
}
iframe {
    margin:0;
    padding:0;
    height:100%;
    width:100%;
    overflow-y:scroll;
    border:none;
}
a:link, a:visited {
    text-decoration:none;
    color:var(--link-color);
}
a[href]:hover, a[href]:focus {
    text-decoration:none;
    color:var(--link-color-active);
}
pre {
    font-family:var(--code-font-family);
    font-size:1em;
}
h1 {
    font-size:1.428em;
}
h2 {
    font-size:1.285em;
}
h3 {
    font-size:1.14em;
}
h4 {
    font-size:1.072em;
}
h5 {
    font-size:1.001em;
}
h6 {
    font-size:0.93em;
}
/* Disable font boosting for selected elements */
h1, h2, h3, h4, h5, h6, div.member-signature {
    max-height: 1000em;
}
ul {
    list-style-type:disc;
}
code, tt {
    font-family:var(--code-font-family);
}
:not(h1, h2, h3, h4, h5, h6) > code,
:not(h1, h2, h3, h4, h5, h6) > tt {
    font-size:var(--code-font-size);
    padding-top:4px;
    margin-top:8px;
    line-height:1.4em;
}
dt code {
    font-family:var(--code-font-family);
    font-size:1em;
    padding-top:4px;
}
.summary-table dt code {
    font-family:var(--code-font-family);
    font-size:1em;
    vertical-align:top;
    padding-top:4px;
}
sup {
    font-size:8px;
}
button {
    font-family: var(--body-font-family);
    font-size: 1em;
}
/*
 * Styles for HTML generated by javadoc.
 *
 * These are style classes that are used by the standard doclet to generate HTML documentation.
 */

/*
 * Styles for document title and copyright.
 */
.about-language {
    float:right;
    padding:0 21px 8px 8px;
    font-size:0.915em;
    margin-top:-9px;
    height:2.9em;
}
.legal-copy {
    margin-left:.5em;
}
/*
 * Styles for navigation bar.
 */
@media screen {
    div.flex-box {
        position:fixed;
        display:flex;
        flex-direction:column;
        height: 100%;
        width: 100%;
    }
    header.flex-header {
        flex: 0 0 auto;
    }
    div.flex-content {
        flex: 1 1 auto;
        overflow-y: auto;
    }
}
.top-nav {
    background-color:var(--navbar-background-color);
    color:var(--navbar-text-color);
    float:left;
    width:100%;
    clear:right;
    min-height:2.8em;
    padding:10px 0 0 0;
    overflow:hidden;
    font-size:0.857em;
}
button#navbar-toggle-button {
    display:none;
}
ul.sub-nav-list-small {
    display: none;
}
.sub-nav {
    background-color:var(--subnav-background-color);
    float:left;
    width:100%;
    overflow:hidden;
    font-size:0.857em;
}
.sub-nav div {
    clear:left;
    float:left;
    padding:6px;
    text-transform:uppercase;
}
.sub-nav .sub-nav-list {
    padding-top:4px;
}
ul.nav-list {
    display:block;
    margin:0 25px 0 0;
    padding:0;
}
ul.sub-nav-list {
    float:left;
    margin:0 25px 0 0;
    padding:0;
}
ul.nav-list li {
    list-style:none;
    float:left;
    padding: 5px 6px;
    text-transform:uppercase;
}
.sub-nav .nav-list-search {
    float:right;
    margin:0;
    padding:6px;
    clear:none;
    text-align:right;
    position:relative;
}
ul.sub-nav-list li {
    list-style:none;
    float:left;
}
.top-nav a:link, .top-nav a:active, .top-nav a:visited {
    color:var(--navbar-text-color);
    text-decoration:none;
    text-transform:uppercase;
}
.top-nav a:hover {
    color:var(--link-color-active);
}
.nav-bar-cell1-rev {
    background-color:var(--selected-background-color);
    color:var(--selected-text-color);
    margin: auto 5px;
}
.skip-nav {
    position:absolute;
    top:auto;
    left:-9999px;
    overflow:hidden;
}
/*
 * Hide navigation links and search box in print layout
 */
@media print {
    ul.nav-list, div.sub-nav  {
        display:none;
    }
}
/*
 * Styles for page header.
 */
.title {
    color:var(--title-color);
    margin:10px 0;
}
.sub-title {
    margin:5px 0 0 0;
}
ul.contents-list {
    margin: 0 0 15px 0;
    padding: 0;
    list-style: none;
}
ul.contents-list li {
    font-size:0.93em;
}
/*
 * Styles for headings.
 */
body.class-declaration-page .summary h2,
body.class-declaration-page .details h2,
body.class-use-page h2,
body.module-declaration-page .block-list h2 {
    font-style: italic;
    padding:0;
    margin:15px 0;
}
body.class-declaration-page .summary h3,
body.class-declaration-page .details h3,
body.class-declaration-page .summary .inherited-list h2 {
    background-color:var(--subnav-background-color);
    border:1px solid var(--border-color);
    margin:0 0 6px -8px;
    padding:7px 5px;
}
/*
 * Styles for page layout containers.
 */
main {
    clear:both;
    padding:10px 20px;
    position:relative;
}
dl.notes > dt {
    font-family: var(--body-font-family);
    font-size:0.856em;
    font-weight:bold;
    margin:10px 0 0 0;
    color:var(--body-text-color);
}
dl.notes > dd {
    margin:5px 10px 10px 0;
    font-size:1em;
    font-family:var(--block-font-family)
}
dl.name-value > dt {
    margin-left:1px;
    font-size:1.1em;
    display:inline;
    font-weight:bold;
}
dl.name-value > dd {
    margin:0 0 0 1px;
    font-size:1.1em;
    display:inline;
}
/*
 * Styles for lists.
 */
li.circle {
    list-style:circle;
}
ul.horizontal li {
    display:inline;
    font-size:0.9em;
}
div.inheritance {
    margin:0;
    padding:0;
}
div.inheritance div.inheritance {
    margin-left:2em;
}
ul.block-list,
ul.details-list,
ul.member-list,
ul.summary-list {
    margin:10px 0 10px 0;
    padding:0;
}
ul.block-list > li,
ul.details-list > li,
ul.member-list > li,
ul.summary-list > li {
    list-style:none;
    margin-bottom:15px;
    line-height:1.4;
}
ul.ref-list {
  padding:0;
  margin:0;
}
ul.ref-list > li {
    list-style:none;
}
.summary-table dl, .summary-table dl dt, .summary-table dl dd {
    margin-top:0;
    margin-bottom:1px;
}
ul.tag-list, ul.tag-list-long {
    padding-left: 0;
    list-style: none;
}
ul.tag-list li {
    display: inline;
}
ul.tag-list li:not(:last-child):after,
ul.tag-list-long li:not(:last-child):after
{
    content: ", ";
    white-space: pre-wrap;
}
ul.preview-feature-list {
    list-style: none;
    margin:0;
    padding:0.1em;
    line-height: 1.6em;
}
/*
 * Styles for tables.
 */
.summary-table, .details-table {
    width:100%;
    border-spacing:0;
    border:1px solid var(--border-color);
    border-top:0;
    padding:0;
}
.caption {
    position:relative;
    text-align:left;
    background-repeat:no-repeat;
    color:var(--selected-text-color);
    clear:none;
    overflow:hidden;
    padding: 10px 0 0 1px;
    margin:0;
}
.caption a:link, .caption a:visited {
    color:var(--selected-link-color);
}
.caption a:hover,
.caption a:active {
    color:var(--navbar-text-color);
}
.caption span {
    font-weight:bold;
    white-space:nowrap;
    padding:5px 12px 7px 12px;
    display:inline-block;
    float:left;
    background-color:var(--selected-background-color);
    border: none;
    height:16px;
}
div.table-tabs {
    padding:10px 0 0 1px;
    margin:10px 0 0 0;
}
div.table-tabs > button {
    border: none;
    cursor: pointer;
    padding: 5px 12px 7px 12px;
    font-weight: bold;
    margin-right: 8px;
}
div.table-tabs > .active-table-tab {
    background: var(--selected-background-color);
    color: var(--selected-text-color);
}
div.table-tabs > button.table-tab {
    background: var(--navbar-background-color);
    color: var(--navbar-text-color);
}
.two-column-search-results {
    display: grid;
    grid-template-columns: minmax(400px, max-content) minmax(400px, auto);
}
div.checkboxes {
    line-height: 2em;
}
div.checkboxes > span {
    margin-left: 10px;
}
div.checkboxes > label {
    margin-left: 8px;
    white-space: nowrap;
}
div.checkboxes > label > input {
    margin: 0 2px;
}
.two-column-summary {
    display: grid;
    grid-template-columns: minmax(25%, max-content) minmax(25%, auto);
}
.three-column-summary {
    display: grid;
    grid-template-columns: minmax(15%, max-content) minmax(20%, max-content) minmax(20%, auto);
}
.three-column-release-summary {
    display: grid;
    grid-template-columns: minmax(40%, max-content) minmax(10%, max-content) minmax(40%, auto);
}
.four-column-summary {
    display: grid;
    grid-template-columns: minmax(10%, max-content) minmax(15%, max-content) minmax(15%, max-content) minmax(15%, auto);
}
@media screen and (max-width: 1000px) {
    .four-column-summary {
        display: grid;
        grid-template-columns: minmax(15%, max-content) minmax(15%, auto);
    }
}
@media screen and (max-width: 800px) {
    .two-column-search-results {
        display: grid;
        grid-template-columns: minmax(40%, max-content) minmax(40%, auto);
    }
    .three-column-summary {
        display: grid;
        grid-template-columns: minmax(10%, max-content) minmax(25%, auto);
    }
    .three-column-release-summary {
        display: grid;
        grid-template-columns: minmax(70%, max-content) minmax(30%, max-content)
    }
    .three-column-summary .col-last,
    .three-column-release-summary .col-last{
        grid-column-end: span 2;
    }
}
@media screen and (max-width: 600px) {
    .two-column-summary {
        display: grid;
        grid-template-columns: 1fr;
    }
}
.summary-table > div, .details-table > div {
    text-align:left;
    padding: 8px 3px 3px 7px;
    overflow-x: auto;
    scrollbar-width: thin;
}
.col-first, .col-second, .col-last, .col-constructor-name, .col-summary-item-name {
    vertical-align:top;
    padding-right:0;
    padding-top:8px;
    padding-bottom:3px;
}
.table-header {
    background:var(--subnav-background-color);
    font-weight: bold;
}
/* Sortable table columns */
.table-header[onclick] {
    cursor: pointer;
}
.table-header[onclick]::after {
    content:"";
    display:inline-block;
    background-image:url('data:image/svg+xml; utf8, \
    <svg xmlns="http://www.w3.org/2000/svg" width="125" height="170"> \
    <path d="M10.101 57.059L63.019 4.142l52.917 52.917M10.101 86.392l52.917 52.917 52.917-52.917" style="opacity:.35;"/></svg>');
    background-size:100% 100%;
    width:9px;
    height:14px;
    margin-left:4px;
    margin-bottom:-3px;
}
.table-header[onclick].sort-asc::after {
    background-image:url('data:image/svg+xml; utf8, \
    <svg xmlns="http://www.w3.org/2000/svg" width="125" height="170"> \
    <path d="M10.101 57.059L63.019 4.142l52.917 52.917" style="opacity:.75;"/> \
    <path d="M10.101 86.392l52.917 52.917 52.917-52.917" style="opacity:.35;"/></svg>');

}
.table-header[onclick].sort-desc::after {
    background-image:url('data:image/svg+xml; utf8, \
    <svg xmlns="http://www.w3.org/2000/svg" width="125" height="170"> \
    <path d="M10.101 57.059L63.019 4.142l52.917 52.917" style="opacity:.35;"/> \
    <path d="M10.101 86.392l52.917 52.917 52.917-52.917" style="opacity:.75;"/></svg>');
}
.col-first, .col-first {
    font-size:0.93em;
}
.col-second, .col-second, .col-last, .col-constructor-name, .col-summary-item-name, .col-last {
    font-size:0.93em;
}
.col-first, .col-second, .col-constructor-name {
    vertical-align:top;
    overflow: auto;
}
.col-last {
    white-space:normal;
}
.col-first a:link, .col-first a:visited,
.col-second a:link, .col-second a:visited,
.col-first a:link, .col-first a:visited,
.col-second a:link, .col-second a:visited,
.col-constructor-name a:link, .col-constructor-name a:visited,
.col-summary-item-name a:link, .col-summary-item-name a:visited {
    font-weight:bold;
}
.even-row-color, .even-row-color .table-header {
    background-color:var(--even-row-color);
}
.odd-row-color, .odd-row-color .table-header {
    background-color:var(--odd-row-color);
}
/*
 * Styles for contents.
 */
div.block {
    font-size:var(--body-font-size);
    font-family:var(--block-font-family);
}
.col-last div {
    padding-top:0;
}
.col-last a {
    padding-bottom:3px;
}
.module-signature,
.package-signature,
.type-signature,
.member-signature {
    font-family:var(--code-font-family);
    font-size:1em;
    margin:14px 0;
    white-space: pre-wrap;
}
.module-signature,
.package-signature,
.type-signature {
    margin-top: 0;
}
.member-signature .type-parameters-long,
.member-signature .parameters,
.member-signature .exceptions {
    display: inline-block;
    vertical-align: top;
    white-space: pre;
}
.member-signature .type-parameters {
    white-space: normal;
}
/*
 * Styles for formatting effect.
 */
.source-line-no {
    /* Color of line numbers in source pages can be set via custom property below */
    color:var(--source-linenumber-color, green);
    padding:0 30px 0 0;
}
.block {
    display:block;
    margin:0 10px 5px 0;
    color:var(--block-text-color);
}
.deprecated-label, .description-from-type-label, .implementation-label, .member-name-link,
.module-label-in-package, .module-label-in-type, .package-label-in-type,
.package-hierarchy-label, .type-name-label, .type-name-link, .search-tag-link, .preview-label {
    font-weight:bold;
}
.deprecation-comment, .help-footnote, .preview-comment {
    font-style:italic;
}
.deprecation-block {
    font-size:1em;
    font-family:var(--block-font-family);
    border-style:solid;
    border-width:thin;
    border-radius:10px;
    padding:10px;
    margin-bottom:10px;
    margin-right:10px;
    display:inline-block;
}
.preview-block {
    font-size:1em;
    font-family:var(--block-font-family);
    border-style:solid;
    border-width:thin;
    border-radius:10px;
    padding:10px;
    margin-bottom:10px;
    margin-right:10px;
    display:inline-block;
}
div.block div.deprecation-comment {
    font-style:normal;
}
details.invalid-tag, span.invalid-tag {
    font-size:1em;
    font-family:var(--block-font-family);
    color: var(--invalid-tag-text-color);
    background: var(--invalid-tag-background-color);
    border: thin solid var(--table-border-color);
    border-radius:2px;
    padding: 2px 4px;
    display:inline-block;
}
details summary {
    cursor: pointer;
}
/*
 * Styles specific to HTML5 elements.
 */
main, nav, header, footer, section {
    display:block;
}
/*
 * Styles for javadoc search.
 */
.ui-state-active {
    /* Overrides the color of selection used in jQuery UI */
    background: var(--selected-background-color);
    border: 1px solid var(--selected-background-color);
    color: var(--selected-text-color);
}
.ui-autocomplete-category {
    font-weight:bold;
    font-size:15px;
    padding:7px 0 7px 3px;
    background-color:var(--navbar-background-color);
    color:var(--navbar-text-color);
}
.ui-autocomplete {
    max-height:85%;
    max-width:65%;
    overflow-y:auto;
    overflow-x:auto;
    scrollbar-width: thin;
    white-space:nowrap;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}
ul.ui-autocomplete {
    position:fixed;
    z-index:1;
    background-color: var(--body-background-color);
}
ul.ui-autocomplete li {
    float:left;
    clear:both;
    min-width:100%;
}
ul.ui-autocomplete li.ui-static-link {
    position:sticky;
    bottom:0;
    left:0;
    background: var(--subnav-background-color);
    padding: 5px 0;
    font-family: var(--body-font-family);
    font-size: 0.93em;
    font-weight: bolder;
    z-index: 2;
}
li.ui-static-link a, li.ui-static-link a:visited {
    text-decoration:none;
    color:var(--link-color);
    float:right;
    margin-right:20px;
}
.ui-autocomplete .result-item {
    font-size: inherit;
}
.ui-autocomplete .result-highlight {
    font-weight:bold;
}
#search-input, #page-search-input {
    background-image:url('resources/glass.png');
    background-size:13px;
    background-repeat:no-repeat;
    background-position:2px 3px;
    background-color: var(--search-input-background-color);
    color: var(--search-input-text-color);
    border-color: var(--border-color);
    padding-left:20px;
    width: 250px;
    margin: 0;
}
#search-input {
    margin-left: 4px;
}
#reset-button {
    background-color: transparent;
    background-image:url('resources/x.png');
    background-repeat:no-repeat;
    background-size:contain;
    border:0;
    border-radius:0;
    width:12px;
    height:12px;
    position:absolute;
    right:12px;
    top:10px;
    font-size:0;
}
::placeholder {
    color:var(--search-input-placeholder-color);
    opacity: 1;
}
.search-tag-desc-result {
    font-style:italic;
    font-size:11px;
}
.search-tag-holder-result {
    font-style:italic;
    font-size:12px;
}
.search-tag-result:target {
    background-color:var(--search-tag-highlight-color);
}
details.page-search-details {
    display: inline-block;
}
div#result-container {
    font-size: 1em;
}
div#result-container a.search-result-link {
    padding: 0;
    margin: 4px 0;
    width: 100%;
}
#result-container .result-highlight {
    font-weight:bolder;
}
.page-search-info {
    background-color: var(--subnav-background-color);
    border-radius: 3px;
    border: 0 solid var(--border-color);
    padding: 0 8px;
    overflow: hidden;
    height: 0;
    transition: all 0.2s ease;
}
div.table-tabs > button.table-tab {
    background: var(--navbar-background-color);
    color: var(--navbar-text-color);
}
.page-search-header {
    padding: 5px 12px 7px 12px;
    font-weight: bold;
    margin-right: 3px;
    background-color:var(--navbar-background-color);
    color:var(--navbar-text-color);
    display: inline-block;
}
button.page-search-header {
    border: none;
    cursor: pointer;
}
span#page-search-link {
    text-decoration: underline;
}
.module-graph span, .sealed-graph span {
    display:none;
    position:absolute;
}
.module-graph:hover span, .sealed-graph:hover span {
    display:block;
    margin: -100px 0 0 100px;
    z-index: 1;
}
.inherited-list {
    margin: 10px 0 10px 0;
}
section.class-description {
    line-height: 1.4;
}
.summary section[class$="-summary"], .details section[class$="-details"],
.class-uses .detail, .serialized-class-details {
    padding: 0 20px 5px 10px;
    border: 1px solid var(--border-color);
    background-color: var(--section-background-color);
}
.inherited-list, section[class$="-details"] .detail {
    padding:0 0 5px 8px;
    background-color:var(--detail-background-color);
    border:none;
}
.vertical-separator {
    padding: 0 5px;
}
ul.help-section-list {
    margin: 0;
}
ul.help-subtoc > li {
  display: inline-block;
  padding-right: 5px;
  font-size: smaller;
}
ul.help-subtoc > li::before {
  content: "\2022" ;
  padding-right:2px;
}
.help-note {
    font-style: italic;
}
/*
 * Indicator icon for external links.
 */
main a[href*="://"]::after {
    content:"";
    display:inline-block;
    background-image:url('data:image/svg+xml; utf8, \
      <svg xmlns="http://www.w3.org/2000/svg" width="768" height="768">\
        <path d="M584 664H104V184h216V80H0v688h688V448H584zM384 0l132 \
        132-240 240 120 120 240-240 132 132V0z" fill="%234a6782"/>\
      </svg>');
    background-size:100% 100%;
    width:7px;
    height:7px;
    margin-left:2px;
    margin-bottom:4px;
}
main a[href*="://"]:hover::after,
main a[href*="://"]:focus::after {
    background-image:url('data:image/svg+xml; utf8, \
      <svg xmlns="http://www.w3.org/2000/svg" width="768" height="768">\
        <path d="M584 664H104V184h216V80H0v688h688V448H584zM384 0l132 \
        132-240 240 120 120 240-240 132 132V0z" fill="%23bb7a2a"/>\
      </svg>');
}
/*
 * Styles for header/section anchor links
 */
a.anchor-link {
    opacity: 0;
    transition: opacity 0.1s;
}
:hover > a.anchor-link {
    opacity: 80%;
}
a.anchor-link:hover,
a.anchor-link:focus-visible,
a.anchor-link.visible {
    opacity: 100%;
}
a.anchor-link > img {
    width: 0.9em;
    height: 0.9em;
}
/*
 * Styles for copy-to-clipboard buttons
 */
button.copy {
    opacity: 70%;
    border: none;
    border-radius: 3px;
    position: relative;
    background:none;
    transition: opacity 0.3s;
    cursor: pointer;
}
:hover > button.copy {
    opacity: 80%;
}
button.copy:hover,
button.copy:active,
button.copy:focus-visible,
button.copy.visible {
    opacity: 100%;
}
button.copy img {
    position: relative;
    background: none;
    filter: brightness(var(--copy-icon-brightness));
}
button.copy:active {
    background-color: var(--copy-button-background-color-active);
}
button.copy span {
    color: var(--body-text-color);
    position: relative;
    top: -0.1em;
    transition: all 0.1s;
    font-size: 0.76rem;
    line-height: 1.2em;
    opacity: 0;
}
button.copy:hover span,
button.copy:focus-visible span,
button.copy.visible span {
    opacity: 100%;
}
/* search page copy button */
button#page-search-copy {
    margin-left: 0.4em;
    padding:0.3em;
    top:0.13em;
}
button#page-search-copy img {
    width: 1.2em;
    height: 1.2em;
    padding: 0.01em 0;
    top: 0.15em;
}
button#page-search-copy span {
    color: var(--body-text-color);
    line-height: 1.2em;
    padding: 0.2em;
    top: -0.18em;
}
div.page-search-info:hover button#page-search-copy span {
    opacity: 100%;
}
/* snippet copy button */
button.snippet-copy {
    position: absolute;
    top: 6px;
    right: 6px;
    height: 1.7em;
    padding: 2px;
}
button.snippet-copy img {
    width: 18px;
    height: 18px;
    padding: 0.05em 0;
}
button.snippet-copy span {
    line-height: 1.2em;
    padding: 0.2em;
    position: relative;
    top: -0.5em;
}
div.snippet-container:hover button.snippet-copy span {
    opacity: 100%;
}
/*
 * Styles for user-provided tables.
 *
 * borderless:
 *      No borders, vertical margins, styled caption.
 *      This style is provided for use with existing doc comments.
 *      In general, borderless tables should not be used for layout purposes.
 *
 * plain:
 *      Plain borders around table and cells, vertical margins, styled caption.
 *      Best for small tables or for complex tables for tables with cells that span
 *      rows and columns, when the "striped" style does not work well.
 *
 * striped:
 *      Borders around the table and vertical borders between cells, striped rows,
 *      vertical margins, styled caption.
 *      Best for tables that have a header row, and a body containing a series of simple rows.
 */

table.borderless,
table.plain,
table.striped {
    margin-top: 10px;
    margin-bottom: 10px;
}
table.borderless > caption,
table.plain > caption,
table.striped > caption {
    font-weight: bold;
    font-size: smaller;
}
table.borderless th, table.borderless td,
table.plain th, table.plain td,
table.striped th, table.striped td {
    padding: 2px 5px;
}
table.borderless,
table.borderless > thead > tr > th, table.borderless > tbody > tr > th, table.borderless > tr > th,
table.borderless > thead > tr > td, table.borderless > tbody > tr > td, table.borderless > tr > td {
    border: none;
}
table.borderless > thead > tr, table.borderless > tbody > tr, table.borderless > tr {
    background-color: transparent;
}
table.plain {
    border-collapse: collapse;
    border: 1px solid var(--table-border-color);
}
table.plain > thead > tr, table.plain > tbody tr, table.plain > tr {
    background-color: transparent;
}
table.plain > thead > tr > th, table.plain > tbody > tr > th, table.plain > tr > th,
table.plain > thead > tr > td, table.plain > tbody > tr > td, table.plain > tr > td {
    border: 1px solid var(--table-border-color);
}
table.striped {
    border-collapse: collapse;
    border: 1px solid var(--table-border-color);
}
table.striped > thead {
    background-color: var(--subnav-background-color);
}
table.striped > thead > tr > th, table.striped > thead > tr > td {
    border: 1px solid var(--table-border-color);
}
table.striped > tbody > tr:nth-child(even) {
    background-color: var(--odd-row-color)
}
table.striped > tbody > tr:nth-child(odd) {
    background-color: var(--even-row-color)
}
table.striped > tbody > tr > th, table.striped > tbody > tr > td {
    border-left: 1px solid var(--table-border-color);
    border-right: 1px solid var(--table-border-color);
}
table.striped > tbody > tr > th {
    font-weight: normal;
}
/**
 * Tweak style for small screens.
 */
@media screen and (max-width: 920px) {
    header.flex-header {
        max-height: 100vh;
        overflow-y: auto;
    }
    div#navbar-top {
        height: 2.8em;
        transition: height 0.35s ease;
    }
    ul.nav-list {
        display: block;
        width: 40%;
        float:left;
        clear: left;
        margin: 10px 0 0 0;
        padding: 0;
    }
    ul.nav-list li {
        float: none;
        padding: 6px;
        margin-left: 10px;
        margin-top: 2px;
    }
    ul.sub-nav-list-small {
        display:block;
        height: 100%;
        width: 50%;
        float: right;
        clear: right;
        background-color: var(--subnav-background-color);
        color: var(--body-text-color);
        margin: 6px 0 0 0;
        padding: 0;
    }
    ul.sub-nav-list-small ul {
        padding-left: 20px;
    }
    ul.sub-nav-list-small a:link, ul.sub-nav-list-small a:visited {
        color:var(--link-color);
    }
    ul.sub-nav-list-small a:hover {
        color:var(--link-color-active);
    }
    ul.sub-nav-list-small li {
        list-style:none;
        float:none;
        padding: 6px;
        margin-top: 1px;
        text-transform:uppercase;
    }
    ul.sub-nav-list-small > li {
        margin-left: 10px;
    }
    ul.sub-nav-list-small li p {
        margin: 5px 0;
    }
    div#navbar-sub-list {
        display: none;
    }
    .top-nav a:link, .top-nav a:active, .top-nav a:visited {
        display: block;
    }
    button#navbar-toggle-button {
        width: 3.4em;
        height: 2.8em;
        background-color: transparent;
        display: block;
        float: left;
        border: 0;
        margin: 0 10px;
        cursor: pointer;
        font-size: 10px;
    }
    button#navbar-toggle-button .nav-bar-toggle-icon {
        display: block;
        width: 24px;
        height: 3px;
        margin: 1px 0 4px 0;
        border-radius: 2px;
        transition: all 0.1s;
        background-color: var(--navbar-text-color);
    }
    button#navbar-toggle-button.expanded span.nav-bar-toggle-icon:nth-child(1) {
        transform: rotate(45deg);
        transform-origin: 10% 10%;
        width: 26px;
    }
    button#navbar-toggle-button.expanded span.nav-bar-toggle-icon:nth-child(2) {
        opacity: 0;
    }
    button#navbar-toggle-button.expanded span.nav-bar-toggle-icon:nth-child(3) {
        transform: rotate(-45deg);
        transform-origin: 10% 90%;
        width: 26px;
    }
}
@media screen and (max-width: 800px) {
    .about-language {
        padding-right: 16px;
    }
    ul.nav-list li {
        margin-left: 5px;
    }
    ul.sub-nav-list-small > li {
        margin-left: 5px;
    }
    main {
        padding: 10px;
    }
    .summary section[class$="-summary"], .details section[class$="-details"],
    .class-uses .detail, .serialized-class-details {
        padding: 0 8px 5px 8px;
    }
    body {
        -webkit-text-size-adjust: none;
    }
}
@media screen and (max-width: 400px) {
    .about-language {
        font-size: 10px;
        padding-right: 12px;
    }
}
@media screen and (max-width: 400px) {
    .nav-list-search {
        width: 94%;
    }
    #search-input, #page-search-input {
        width: 70%;
    }
}
@media screen and (max-width: 320px) {
    .nav-list-search > label {
        display: none;
    }
    .nav-list-search {
        width: 90%;
    }
    #search-input, #page-search-input {
        width: 80%;
    }
}

pre.snippet {
    background-color: var(--snippet-background-color);
    color: var(--snippet-text-color);
    padding: 10px;
    margin: 12px 0;
    overflow: auto;
    white-space: pre;
}
div.snippet-container {
    position: relative;
}
@media screen and (max-width: 800px) {
    pre.snippet {
        padding-top: 26px;
    }
    button.snippet-copy {
        top: 4px;
        right: 4px;
    }
}
pre.snippet .italic {
    font-style: italic;
}
pre.snippet .bold {
    font-weight: bold;
}
pre.snippet .highlighted {
    background-color: var(--snippet-highlight-color);
    border-radius: 10%;
}
