
/*class Main5{
    public static void main(String[] args){
        Scanner sc = new Scanner(System.in);   //how to convert the primitive dt using copyOf on dt to another
        int s = sc.nextInt();
        int[] arr = new int[s];
        //int [] arr1 = new int[s];
        Integer [] arr1 = new Integer[s];
        
        for(int i=0;i<s;i++){
            arr[i] = sc.nextInt();
        }
        System.out.println("elements in array:");
        for(int i=0;i<s;i++){
            System.out.println(arr[i]);
        }
        arr1 = Arrays.copyOf(arr,s);
        }
}*/

public class Main5{
    Integer id;
    String name;

    public Main5(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    
    
}