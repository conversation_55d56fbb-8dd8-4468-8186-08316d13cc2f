/*public class hello{
    public static void main(String[] args) {
		Person person1 = new Person("<PERSON>", 25);
		Person person2 = new Person("<PERSON>", 30);
		person1.sayHello();
		person2.sayHello();
    }

    private static class Person {

        public Person(String alice, int par) {
        }

        private void sayHello() {
            System.out.println("name displayed");
        }
    }
}


//invoking java compiler have to use --> javc file_name
//to invoke jvm have to use -->java class_name

*/

public class hello{
    public static void main(String[] args) {
        One one = new One();
        Two two = new Two();
        one.display();
        two.display();
        System.out.println("String "+args[0]);
        System.out.println("Integer "+Integer.parseInt(args[1]));


    }
}

class One{
    public void display(){
        System.out.println("hello from one");
    }
}

class Two{
    public void display(){
        System.out.println("hello from two");
    }
}
