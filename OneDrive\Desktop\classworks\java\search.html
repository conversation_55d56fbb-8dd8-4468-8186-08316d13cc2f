<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) on Tue Jul 08 14:09:31 IST 2025 -->
<title>Search</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="dc.created" content="2025-07-08">
<meta name="description" content="search">
<meta name="generator" content="javadoc/SearchWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="search-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#search">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<h1 class="title">Search</h1>
<div>
<input type="text" id="page-search-input" disabled placeholder="Search">
<input type="reset" id="page-search-reset" disabled value="Reset" style="margin: 6px;">
<details class="page-search-details">
<summary id="page-search-expand">Additional resources</summary>
</details>
</div>
<div class="page-search-info">
<p>The <a href="help-doc.html#search">help page</a> provides an introduction to the scope and syntax of JavaDoc search.</p>
<p>You can use the &lt;ctrl&gt; or &lt;cmd&gt; keys in combination with the left and right arrow keys to switch between result tabs in this page.</p>
<p>The URL template below may be used to configure this page as a search engine in browsers that support this feature. It has been tested to work in Google Chrome and Mozilla Firefox. Note that other browsers may not support this feature or require a different URL format.</p>
<span id="page-search-link">link</span><button class="copy" aria-label="Copy URL" id="page-search-copy"><img src="copy.svg" alt="Copy URL"><span data-copied="Copied!">Copy</span></button>
<p>
<input type="checkbox" id="search-redirect" disabled>
<label for="search-redirect">Redirect to first result</label></p>
</div>
<p id="page-search-notify">Loading search index...</p>
<div id="result-section" style="display: none;">
<div id="result-container"></div>
<script type="text/javascript" src="search-page.js"></script>
</div>
</main>
</div>
</div>
</body>
</html>
