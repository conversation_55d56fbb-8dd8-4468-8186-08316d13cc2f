
import java.util.Scanner;

public class Student5{

    
        public static void main(String[] args) {
            Scanner sc = new Scanner(System.in);
                int s = Integer.parseInt(sc.nextLine());
                Main5[] arr = new Main5[s];
                Integer id;
                String name;
                Main5 ss;
                for(int i=0;i<s;i++){
                    System.out.println("enter "+(i+1)+" student data");
                    System.out.println("id:");
                    id =Integer.parseInt(sc.nextLine());
                    System.out.println("Name:");
                    name=sc.nextLine();
                    ss = new Main5(id,name);
                    arr[i]=ss;
                }
                for(Main5 s1 : arr){
                    System.out.println(s1);
                }
        
    }
    }