

/*public class day4{
    public static void main(String[] args) throws IOException {
        int a;
        Integer b;
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        a=Integer.parseInt(br.readLine());
        b=Integer.parseInt(br.readLine());
        System.out.println(a+" "+b);  //convering int to string

    }
}*/

/*public class day4{
    public static void main(String[] args)throws IOException {
        float a;
        Float b;
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        a=Float.parseFloat(br.readLine());
        b=Float.parseFloat(br.readLine());
        System.out.println(a+" "+b);
            
        }
    }
    */
/*public class day4{
    public static void main(String[] args)throws IOException {
        float a;
        Float b;
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        a=Float.parseFloat(br.readLine());
        b=Float.parseFloat(br.readLine());
        System.out.printf("%.4f %.4f%n", a, b); //34.0000 56.0000
        System.out.printf("%4f %4f%n", a, b); //34.000000 56.000000
        System.out.printf("%4.2f %4.2f%n", a, b); //34.00 56.00
            
        }
    }
*/
/*
public class day4{
    public static void main(String[] args)throws IOException {
        Scanner sc = new Scanner(System.in);
        System.out.print("enter a character:");
        char ch = sc.next().charAt(0);
        System.out.print("the entered character is: ");
        System.out.println(ch);
            
        }
    }


public class day4{
    public static void main(String[] args)throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        System.out.print("enter a character:");
        char b = br.readLine().charAt(0);
        System.out.print("entered character:"+b);       
        }
    }
*/
import java.io.*;
public class day4{
    public static void main(String[] args) throws IOException {
        int a = 0;
        char b;
        float f;
        String s;
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in)); //in file and appliance, the opened one should be closed to let the next program to access it.        a = Integer.parseInt(br.readLine());
        b = br.readLine().charAt(0); //0 is the index
        f = Float.parseFloat(br.readLine());
        s = br.readLine();
        System.out.println("integer:"+a);
        System.out.println("char:"+b);
        System.out.println("float:"+f);
        System.out.println("sstring:"+s);
    }

}



//steps for skipping \n - 2 ways - readLine() reads entire line //d