//file clases - path,file,files
//to read files - user reader class, input strings
//to write files - user writer class, output strings
//to read bytes - user inputstream class, input bytes
//to write bytes - user outputstream class, output bytes
//to read and write bytes - user randomaccessfile class, input and output bytes
//to read and write characters - user reader and writer classes, input and output characters
//to read and write objects - user objectinputstream and objectoutputstream classes, input and output objects
/*
writer class - to write data to a file
reader class - to read data from a file
inputstream class - to read bytes from a file
outputstream class - to write bytes to a file
randomaccessfile class - to read and write bytes from a file
reader and writer classes - to read and write characters from a file
objectinputstream and objectoutputstream classes - to read and write objects from a file


write - create new , append - add to existing
FileWriter - to write data to a file
FileReader - to read data from a file
FileInputStream - to read bytes from a file
FileOutputStream - to write bytes to a file
RandomAccessFile - to read and write bytes from a file
BufferedReader - to read characters from a file
BufferedWriter - to write characters to a file
ObjectInputStream - to read objects from a file
ObjectOutputStream - to write objects to a file






















*/