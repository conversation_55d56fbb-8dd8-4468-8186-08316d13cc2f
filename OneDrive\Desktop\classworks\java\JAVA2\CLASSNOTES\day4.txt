AUTOBOXING 
UNBOXING 

AUTOBOXING - AUTOMATICALLY WRAP 

SMALLEST DATATYPE - BYTE

Integer

byte->short->int->Long
           float->Double

byte,short can be stored in int but int cannot be stored in byte . for that, we have to do typecasting- typecasting may lead to loss of data
            parse - to convert string to int,float,double,long,short,byte,char,boolean

Byte   Byte.parseByte()  Byte.valueOf()
Short   Short.parseSort()  Short.valueOf()
Integer  Integer.parseInt() Integer.valueOf()
Long     Long.parseLong()   Long.valueOf()
Float    Float.parseFloat()   Float.valueOf()
Double   Double.parseDouble() Double.valueOf()

BIGINTEGER - FOR VERY LARGE NUMBERS
- Used when numbers exceed the range of long (64 bits)
- Can handle arbitrarily large integers (limited only by memory)
- Part of java.math package
- Immutable class (cannot be changed once created)

BIGINTEGER METHODS:
- BigInteger.valueOf(long) - converts long to BigInteger
- new BigInteger(String) - creates from string
- add(), subtract(), multiply(), divide() - arithmetic operations
- compareTo() - compare two BigInteger values

EXAMPLE:
BigInteger big1 = new BigInteger("123456789012345678901234567890");
BigInteger big2 = BigInteger.valueOf(100);
BigInteger result = big1.add(big2);

WHY USE BIGINTEGER:
- long max value = 9,223,372,036,854,775,807
- BigInteger can handle numbers much larger than this
- Useful for cryptography, mathematical calculations, large data processing

VARIABLE TO STRING CONVERSION IN TWO WAYS
String s = ""+b;   //""+any variable = string
b.toString();   //direct method call //only with objects, not with primitive datatypes


IO CLASSES
1) Reader/Writer -> character based inputs/outputs
2) InputStream/OutputStream -> byte based inputs/outputs
3)Console -> CMD prompts

BufferedReader/BufferedReader - accept any input stream - file, keyboard, network
inputStreamReader / OutputStreamReader

Util package
1) Scanner class - to handle exception - to check on or off