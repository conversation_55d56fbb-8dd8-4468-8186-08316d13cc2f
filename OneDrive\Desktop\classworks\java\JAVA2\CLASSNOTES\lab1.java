/*class lab1{
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        System.out.println("enter number of students:");
        int n = sc.nextInt();
        int arr[] = new int[n];
        String[] name = new String[n];
        int[] marks = new int[3];
        int[] max = new int[n];
        for(int i=0;i<n;i++){
            System.out.print("id:");
            arr[i] = sc.nextInt();
            System.out.print("name:");
            name[i] = sc.next();
            
            for(int j = 0;j<3;j++){
                max[i]= max[j];
                System.out.print("mark:");
                marks[j] = sc.nextInt();
                if(marks[j]>max[i]){
                    max[i] = marks[j];
                }
            }
        }
        for(int i=0;i<n;i++){
            System.out.println("Student ID: "+arr[i]);
            System.out.println("Name: "+name[i]);
            System.out.println(Math.Sort(max[i]));
        }
    }
}*/

class lab1{
    public static void main(String[] args) {
        Integer id;
        String name;
        Integer mark1;
        Integer mark2;
        Integer mark3;
        Integer total;
        
        public lab1(Integer id, String name, Integer mark1, Integer mark2, Integer mark3) {
            this.id = id;
            this.name = name;
            this.mark1 = mark1;
            this.mark2 = mark2;
            this.mark3 = mark3;
            this.total = mark1+mark2+mark3;
        }

    }
}